import chalk from 'chalk';
import { configDotenv } from 'dotenv';
import * as path from 'node:path';
import { createUserStatic } from './test-utils';

export type Prettify<T> = {
  [K in keyof T]: T[K];
} & {};

export type BodyFieldConditions = Record<string, 'skip' | 'present' | 'redacted'>;

export interface EndpointConfig {
  name?: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  params?: Record<string, string>;
  payload?: any;
  headers?: Record<string, string>;
  bodyFieldConditions?: BodyFieldConditions;
  skipHeaders?: string[];
  only?: boolean; // Used to skip any other endpoint that doesn't have only set to true
  workflow?: never;
  useAuth?: 'javiTest' | 'domCraft';
  user?: {
    username: string;
    password: string;
  },
  sortKey?: string; // Sorts array responses by the specified key
}

export type WorkflowEndpointConfig = Prettify<Omit<EndpointConfig, WorkflowLiftUpElements | 'workflow'> & {
  workflowState?: any,
}>

type WorkflowLiftUpElements = 'only' | 'name';

export type StepData = {
  resBody: any;
  params: any;
  prevWorkflowState: any;
};

export type WorkflowConfig = Prettify<Pick<EndpointConfig, WorkflowLiftUpElements> & {
  workflow: ((response: StepData) => WorkflowEndpointConfig)[];
}>

export interface TestConfig {
  classicApi: string;
  wrapperApi: string;
  auth: {
    javiTest: {
      wrapperClientId: string;
      wrapperClientSecret: string;
      classicOauthUsername: string;
      classicOauthPassword: string;
    };
    domCraft: {
      wrapperClientId: string;
      wrapperClientSecret: string;
      classicOauthUsername: string;
      classicOauthPassword: string;
    };
  };
  endpoints: (EndpointConfig | WorkflowConfig)[];
  defaultParams?: Record<string, string>;
  logFullResponses?: boolean;
}

configDotenv({ path: path.join(__dirname, '.env') });

function getBaseUrl(baseUrl: string) {
  if (baseUrl.endsWith('/')) {
    return baseUrl.slice(0, -1);
  }
  return baseUrl;
}

const domCraftAppId = '325570412';
const javiTestAppId = '207800543';
const javiTestUserWithMostPermissions = '998756643';
const javiTestUserWithDob = '719570082';
const javiTestUserUpdateEmail = '718287361';
const javiTestGetUser = '697021635';
const javiGetPermissionsUser = '595412530';
const javiReviewPermissionsUser = '665637837';
const javiActivateAppUser = '615992160';


export const config: TestConfig = {
  classicApi: getBaseUrl(process.env.CLASSIC_API || 'http://localhost:84'),
  wrapperApi: getBaseUrl(process.env.WRAPPER_API || 'http://localhost:85'),
  auth: {
    javiTest: {
      wrapperClientId: process.env.WRAPPER_CLIENT_ID || 'not-provided',
      wrapperClientSecret: process.env.WRAPPER_CLIENT_SECRET || 'not-provided',
      classicOauthUsername: process.env.CLASSIC_OAUTH_USERNAME || 'not-provided',
      classicOauthPassword: process.env.CLASSIC_OAUTH_PASSWORD || 'not-provided',
    },
    domCraft: {
      wrapperClientId: process.env.DOM_CRAFT_WRAPPER_CLIENT_ID || 'not-provided',
      wrapperClientSecret: process.env.DOM_CRAFT_WRAPPER_CLIENT_SECRET || 'not-provided',
      classicOauthUsername: process.env.DOM_CRAFT_CLASSIC_OAUTH_USERNAME || 'not-provided',
      classicOauthPassword: process.env.DOM_CRAFT_CLASSIC_OAUTH_PASSWORD || 'not-provided',
    },
  },
  logFullResponses: false,
  defaultParams: {
    // default params for all endpoints in path
    appId: javiTestAppId,
  },
  endpoints: [
    {
      name: 'Delete activation user flow, single activation v2 endpoint (user account will be deleted)',
      workflow: [
        () => {
          return {
            path: '/v2/apps/:appId/users',
            method: 'POST',
            params: {
              appId: javiTestAppId,
            },
            payload: {
              country: 'GM',
              language: 'en',
              dateOfBirth: '2020-10-10',
              parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
              permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
            },
            bodyFieldConditions: {
              id: 'skip',
              uuid: 'skip',
            },
          };
        },
        ({ resBody }) => {
          return {
            path: '/v2/apps/:appId/users/:userId',
            method: 'DELETE',
            params: {
              userId: resBody.id,
              appId: javiTestAppId,
            },
          };
        },
      ],
    },
    {
      name: 'Delete activation user flow, two activations v1',
      workflow: [
        () => {
          const newUser = createUserStatic(domCraftAppId);
          return {
            name: "Create new user",
            path: '/v2/users',
            method: 'POST',
            payload: {
              ...newUser,
              token: "someTokenThatWeNeed"
            },
            headers: {
              "x-kws-bypass-talon": "true"
            },
            bodyFieldConditions: {
              id: "present"
            },
            workflowState: {
              user: newUser
            }
          };
        },
        ({ resBody, prevWorkflowState }) => {
          const user = {
            ...prevWorkflowState.user,
            id: resBody.id
          }
          return {
            name: "Activate new user with javi-test",
            path: '/v1/users/:userId/apps',
            method: 'POST',
            params: {
              userId: user.id,
            },
            payload: {
              appName: "javi-test",
              permissions: ["Stranger.things", "Greece.Opt-out"],
              dateOfBirth: "2010-01-01",
              parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
            },
            bodyFieldConditions: {
              id: 'skip',
              uuid: 'skip',
            },
            user: user,
            useAuth: "javiTest",
            workflowState: {
              user: user
            }
          };
        },
        ({ prevWorkflowState }) => {
          const user = prevWorkflowState.user;
          return {
            name: "Activate new user with classic dom craft",
            path: '/v1/users/:userId/apps',
            method: 'POST',
            params: {
              userId: user.id,
            },
            payload: {
              appName: "classicdomcraft",
              permissions: ["permissionA"],
              dateOfBirth: "2010-01-01",
              parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
            },
            bodyFieldConditions: {
              id: 'skip',
              uuid: 'skip',
            },
            user: user,
            useAuth: "domCraft",
          };
        },
        ({ prevWorkflowState }) => {
          return {
            path: '/v2/apps/:appId/users/:userId',
            method: 'DELETE',
            params: {
              userId: prevWorkflowState.user.id,
              appId: javiTestAppId,
            },
          };
        },
      ],
    },
    {
      name: 'Delete activation user flow, one activation v1, delete user account',
      workflow: [
        () => {
          const newUser = createUserStatic(domCraftAppId);
          return {
            name: "Create new user",
            path: '/v2/users',
            method: 'POST',
            payload: {
              ...newUser,
              token: "someTokenThatWeNeed"
            },
            headers: {
              "x-kws-bypass-talon": "true"
            },
            bodyFieldConditions: {
              id: "present"
            },
            workflowState: {
              user: newUser
            }
          };
        },
        ({ resBody, prevWorkflowState }) => {
          const user = {
            ...prevWorkflowState.user,
            id: resBody.id
          }
          return {
            name: "Activate new user with javi-test",
            path: '/v1/users/:userId/apps',
            method: 'POST',
            params: {
              userId: user.id,
            },
            payload: {
              appName: "javi-test",
              permissions: ["Stranger.things", "Greece.Opt-out"],
              dateOfBirth: "2010-01-01",
              parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
            },
            bodyFieldConditions: {
              id: 'skip',
              uuid: 'skip',
            },
            user: user,
            useAuth: "javiTest",
            workflowState: {
              user: user
            }
          };
        },
        ({ prevWorkflowState }) => {
          return {
            path: '/v2/apps/:appId/users/:userId',
            method: 'DELETE',
            params: {
              userId: prevWorkflowState.user.id,
              appId: javiTestAppId,
            },
          };
        },
      ],
    },
    {
      name: 'Activate app for new user, two activations',
      workflow: [
        () => {
          const newUser = createUserStatic(domCraftAppId);
          return {
            name: "Create new user",
            path: '/v2/users',
            method: 'POST',
            payload: {
              ...newUser,
              token: "someTokenThatWeNeed"
            },
            headers: {
              "x-kws-bypass-talon": "true"
            },
            bodyFieldConditions: {
              id: "present"
            },
            workflowState: {
              user: newUser
            }
          };
        },
        ({ resBody, prevWorkflowState }) => {
          const user = {
            ...prevWorkflowState.user,
            id: resBody.id
          }
          return {
            name: "Activate new user with javi-test",
            path: '/v1/users/:userId/apps',
            method: 'POST',
            params: {
              userId: user.id,
            },
            payload: {
              appName: "javi-test",
              permissions: ["Stranger.things", "Greece.Opt-out"],
              dateOfBirth: "2010-01-01",
              parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
            },
            bodyFieldConditions: {
              id: 'skip',
              uuid: 'skip',
            },
            user: user,
            useAuth: "javiTest",
            workflowState: {
              user: user
            }
          };
        },
        ({ prevWorkflowState }) => {
          const user = prevWorkflowState.user;
          return {
            name: "Activate new user with classic dom craft",
            path: '/v1/users/:userId/apps',
            method: 'POST',
            params: {
              userId: user.id,
            },
            payload: {
              appName: "classicdomcraft",
              permissions: ["permissionA"],
              dateOfBirth: "2010-01-01",
              parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
            },
            bodyFieldConditions: {
              id: 'skip',
              uuid: 'skip',
            },
            user: user,
            useAuth: "domCraft",
          };
        },
      ],
    },
    {
      name: 'Activating app for user user with an app that is already activated',
      path: '/v1/users/:userId/apps',
      method: 'POST',
      params: {
        userId: javiActivateAppUser,
      },
      payload: {
        appName: 'javi-test',
        permissions: ['Stranger.things', 'Greece.Opt-out'],
        dateOfBirth: '2010-01-01',
        parentEmail: '<EMAIL>',
      },
      bodyFieldConditions: {
        errorMessage: "present"
      }
    },
    {
      name: 'Create user',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        id: 'present',
        uuid: 'skip',
      },
    },
    {
      name: 'Create with without parent email',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        detail: "skip",
        invalid: "skip",
      },
    },
    {
      name: 'Create user without country',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        detail: "skip",
        invalid: "skip",
      },
    },
    {
      name: 'Create user with an invalid country error case',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'INVALID',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        detail: "skip",
        invalid: "skip",
      },
    },
    {
      name: 'Create user with an invalid country that passes classic validations',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'NT',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        id: "present",
        uuid: "present"
      },
    },
    {
      name: 'Create user with no language',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        id: "present",
        uuid: "present"
      },
    },
    {
      name: 'Create user with invalid language',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'nt',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        id: "present",
        uuid: "present"
      },
    },
    {
      name: 'Create user with no date of birth',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        detail: "skip",
        invalid: "skip",
      },
    },
    {
      name: 'Create user with no parent email',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        detail: "skip",
        invalid: "skip",
      },
    },
    {
      name: 'Create user with no permissions',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
      },
      bodyFieldConditions: {
        id: "present",
        uuid: "present"
      },
    },
    {
      name: 'Create user with empty permissions array',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: [],
      },
      bodyFieldConditions: {
        id: "present",
        uuid: "present"
      },
    },
    {
      name: 'Create user with one invalid permission',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: [
          "abc123"
        ],
      },
      bodyFieldConditions: {
        errorMessage: "present",
      },
    },
    {
      name: 'Create user with more than one invalid permission',
      path: '/v2/apps/:appId/users',
      method: 'POST',
      params: {
        appId: javiTestAppId,
      },
      payload: {
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        permissions: [
          "permissionThatDoesNotExist",
          "abc",
          "abc123"
        ],
      },
      bodyFieldConditions: {
        errorMessage: "present",
      },
    },
    {
      name: 'Create user and activate with a second app v2',
      workflow: [
        () => {
          return {
            name: 'Create user',
            path: '/v2/apps/:appId/users',
            method: 'POST',
            params: {
              appId: javiTestAppId,
            },
            payload: {
              country: 'GM',
              language: 'en',
              dateOfBirth: '2020-10-10',
              parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
              permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
            },
            bodyFieldConditions: {
              id: 'present',
              uuid: 'skip',
            },
          };
        },
        ({ resBody }) => {
          return {
            name: "Activating user with another app v2 endpoint",
            path: '/v2/apps/:appId/users/:userId/activate',
            method: 'POST',
            useAuth: "domCraft",
            params: {
              userId: resBody.id,
              appId: domCraftAppId,
            },
            payload: {
              permissions: ["permissionA"]
            },
            bodyFieldConditions: {
              id: 'present',
            },
          };
        },
      ],
    },
    {
      path: '/v2/apps/:appId/users/:userId/request-permissions',
      method: 'POST',
      params: {
        userId: javiReviewPermissionsUser,
      },
      payload: {
        parentEmail: '<EMAIL>',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        'user.parentEmail': 'redacted',
        // parent state flags are all false - see KCMP-248
        'user.parentExpired': 'present',
        'user.parentIdVerified': 'present',
        'user.parentRejected': 'present',
        'user.parentVerified': 'present',
      },
    },
    {
      name: 'Request Permissions with date of birth',
      path: '/v2/apps/:appId/users/:userId/request-permissions',
      method: 'POST',
      params: {
        userId: javiTestUserWithDob,
      },
      payload: {
        parentEmail: 'dominic.wild+{random}@xa.epicgames.com',
        dateOfBirth: '2016-09-14',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
      },
      bodyFieldConditions: {
        'user.parentEmail': 'redacted',
      },
    },

    {
      name: 'Request permissions with user that does not have activation for that app',
      workflow: [
        () => {
          return {
            name: 'Create user',
            path: '/v2/apps/:appId/users',
            method: 'POST',
            params: {
              appId: javiTestAppId,
            },
            payload: {
              country: 'GM',
              language: 'en',
              dateOfBirth: '2020-10-10',
              parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
              permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
            },
            bodyFieldConditions: {
              id: 'present',
              uuid: 'skip',
            },
          };
        },
        ({ resBody }) => {
          return {
            name: "Request permissions with user that does not have activation for that app",
            path: '/v2/apps/:appId/users/:userId/request-permissions',
            method: 'POST',
            useAuth: "domCraft",
            params: {
              userId: resBody.id,
              appId: domCraftAppId,
            },
            payload: {
              dateOfBirth: '2016-09-14',
              permissions: ["permissionA"]
            },
            bodyFieldConditions: {
              errorMessage: 'present',
            },
          };
        },
      ],
    },

    {
      name: 'Updating parenting email with already verified parent',
      path: '/v2/apps/:appId/users/:userId/update-parent-email',
      params: {
        userId: javiTestUserUpdateEmail,
      },
      method: 'POST',
      payload: {
        parentEmail: 'dominic.wild+updateEmail-{random}@xa.epicgames.com',
      },
    },
    {
      name: 'Get User',
      path: '/v2/apps/:appId/users/:userId',
      params: {
        userId: javiTestGetUser,
      },
      method: 'GET',
      bodyFieldConditions: {
        activationCreatedAt: 'present',
        uuid: 'present',
        // parent state flags are all false - see KCMP-248
        'parentState.verified': 'present',
        'parentState.idVerified': 'present',
        'parentState.deleted': 'present',
        'parentState.rejected': 'present',
        'parentState.expired': 'present',
      },
    },
    {
      path: '/v2/apps/:appId/users/:userId/permissions',
      params: {
        userId: javiGetPermissionsUser,
      },
      method: 'GET',
    },
    {
      path: '/v2/apps/:appId/users/:userId/permissions?extended=true',
      params: {
        userId: javiGetPermissionsUser,
      },
      method: 'GET',
    },
    {
      path: '/v2/apps/:appId/users/:userId/review-permissions',
      params: {
        userId: javiReviewPermissionsUser,
      },
      method: 'POST',
    },
    {
      name: 'Checking for username needing review',
      path: '/v1/users/check-username?username=MikeHuntIs4Timmy',
      method: 'GET',
      bodyFieldConditions: {
        "details.reasons": "skip"
      }
    },
    {
      name: 'Checking for username that is accepted',
      path: '/v1/users/check-username?username=Hazel',
      method: 'GET',
    },
    {
      name: 'Checking for username that is rejected',
      path: '/v1/users/check-username?username=Suekondeezknots',
      method: 'GET',
      bodyFieldConditions: {
        "details.reasons": "skip"
      }
    },
    {
      name: 'Checking for username that is taken',
      path: '/v1/users/check-username?username=cthulu',
      method: 'GET',
      bodyFieldConditions: {
        "details.reasons": "skip"
      }
    },
    {
      name: "Regular POST /v2/users request",
      path: '/v2/users',
      method: 'POST',
      payload: {
        username: "golden-sun-{random}",
        password: "master-sword",
        parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
        originAppId: +javiTestAppId,
        token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
        dateOfBirth: "2022-05-13"
      },
      headers: {
        "x-kws-bypass-talon": "true"
      },
      bodyFieldConditions: {
        id: "present"
      }
    },
    {
      name: "No origin app ID",
      path: '/v2/users',
      method: 'POST',
      payload: {
        username: "golden-sun-{random}",
        password: "master-sword",
        token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
        dateOfBirth: "2012-05-13"
      },
      headers: {
        "x-kws-bypass-talon": "true"
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        invalid: "skip",
        detail: "skip",
      }
    },
    {
      name: "No date of birth",
      path: '/v2/users',
      method: 'POST',
      payload: {
        username: "golden-sun-{random}",
        password: "master-sword",
        originAppId: +javiTestAppId,
        token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
      },
      headers: {
        "x-kws-bypass-talon": "true"
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        invalid: "skip",
        detail: "skip",
      }
    },
    {
      name: "Missing password",
      path: '/v2/users',
      method: 'POST',
      payload: {
        username: "golden-sun-{random}",
        parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
        originAppId: +javiTestAppId,
        token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
        dateOfBirth: "2012-05-13"
      },
      headers: {
        "x-kws-bypass-talon": "true"
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        invalid: "skip",
        detail: "skip",
      }
    },
    {
      name: "Missing username",
      path: '/v2/users',
      method: 'POST',
      payload: {
        password: "master-sword",
        parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
        originAppId: +javiTestAppId,
        token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
        dateOfBirth: "2012-05-13"
      },
      headers: {
        "x-kws-bypass-talon": "true"
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        invalid: "skip",
        detail: "skip",
      }
    },
    {
      name: "Missing username and password",
      path: '/v2/users',
      method: 'POST',
      payload: {
        parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
        originAppId: +javiTestAppId,
        token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
        dateOfBirth: "2012-05-13"
      },
      headers: {
        "x-kws-bypass-talon": "true"
      },
      bodyFieldConditions: {
        code: "present",
        codeMeaning: "present",
        errorMessage: "present",
        invalid: "skip",
        detail: "skip",
      }
    },
    {
      // It is slightly possible for this to fail if JWK's get out of sync, or a new one is created
      // Just as the requests fire off
      name: "Get latest 20 JWKs",
      path: '/v1/jwks',
      params: {
        userId: javiReviewPermissionsUser,
      },
      method: 'GET',
    },
    {
      name: 'Age gate',
      path: '/v1/countries/child-age?country=GB&dob=2015-12-12',
      method: 'GET',
    },
    {
      name: 'Age gate - lower case country',
      path: '/v1/countries/child-age?country=gb&dob=2015-12-12',
      method: 'GET',
      bodyFieldConditions: {
        country: 'present',
      },
    },
    {
      name: 'Age gate - alias',
      path: '/v1/countries/childage?country=GB&dob=2015-12-12',
      method: 'GET',
    },
    {
      only: true,
      name: 'Get Translation Permissions - All Permissions',
      path: '/v2/apps/:appId/permissions/translated',
      method: 'GET',
      sortKey: "name",
    },
    {
      only: true,
      name: 'Get Translation Permissions - One Permission',
      path: '/v2/apps/:appId/permissions/translated?permissions=complicated',
      method: 'GET',
      sortKey: "name",
    },
    {
      only: true,
      name: 'Get Translation Permissions - Two Permissions',
      path: '/v2/apps/:appId/permissions/translated?permissions=complicated,privacy-policy',
      method: 'GET',
      sortKey: "name",
    },
  ],
};

if (config.endpoints.some((endpoint) => endpoint.only)) {
  console.log(chalk.yellow('🚨 Some endpoint(s) are set to only. Skipping all other endpoints.'));
  const totalEndpoints = config.endpoints.length;
  config.endpoints = config.endpoints.filter((endpoint) => endpoint.only);
  const filteredEndpoints = config.endpoints.length;
  console.log(chalk.yellow(`🚨 Skipped ${totalEndpoints - filteredEndpoints} endpoint(s) / workflow(s).`));
}
