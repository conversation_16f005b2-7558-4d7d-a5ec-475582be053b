import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  CountryRegionExists,
  IsValidLanguage,
  Tiso31662,
  Tiso6391,
  countries,
  ESupportedLanguages,
  TRegionsAsKey,
  TCountryLite,
} from '@superawesome/freekws-regional-config';
import { Expose, Transform, Type } from 'class-transformer';
import {
  ArrayUnique,
  IsBoolean,
  IsDate,
  IsEmail,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  Validate,
} from 'class-validator';

import { DATE_FORMAT_REGEX } from '../common';

const languages = Object.entries(countries).reduce((acc, [countryName, country]) => {
  let lang = country?.languages.split(',').find((lang) => lang.length === 2 || lang.length === 5) ?? 'en';

  if (lang.toLowerCase() === 'zh-tw') {
    lang = 'en';
  }
  if (lang.toLowerCase() === 'zh-cn') {
    lang = ESupportedLanguages.ZhHans;
  }

  lang = lang.split('-')[0];

  return {
    ...acc,
    [countryName]: lang,
  };
}, {} as Record<keyof typeof countries, string>);

export type Permissions = Record<string, boolean | null>;

export class UserCreateDTO {
  @ApiProperty({
    description: 'Country of the user in ISO3166 format.',
    example: 'US',
  })
  @IsString()
  @Matches(/^[A-Z]{2}$/)
  country: Tiso31662;

  @ApiPropertyOptional({
    description: 'User language. Default for country if not presented.',
  })
  @Expose()
  @IsOptional()
  @Transform(
      ({ value, obj }: { value: string; obj: UserCreateDTO }) => {
        if (new IsValidLanguage().validate(value)) {
          return value;
        }
        return languages[obj.country?.toUpperCase() as keyof TRegionsAsKey<TCountryLite>] || 'en'
      })
  language: Tiso6391;

  @ApiProperty({
    description: `Date of birth of the user in YYYY-MM-DD format.`,
    example: '2010-10-10',
  })
  @Matches(DATE_FORMAT_REGEX)
  dateOfBirth: string;

  @ApiPropertyOptional({
    description: "User's parent email. Required if user age is under consent age for the country.",
  })
  @IsEmail()
  parentEmail: string;

  @ApiPropertyOptional({
    description: 'Array of permissions for which consent will be requested.',
  })
  @IsString({ each: true })
  @IsOptional()
  permissions?: string[];
}

export class UserCreateResponseDTO {
  @ApiProperty({
    description: 'User ID',
  })
  @IsNumber()
  @Expose()
  id: number;

  @ApiProperty({
    description: 'User UUID',
  })
  @IsUUID()
  @Expose()
  uuid: string | undefined;

  @ApiProperty({
    description: 'Describes if user is under of digital consent age for the country',
  })
  @IsBoolean()
  @Expose()
  isMinor: boolean;

  @ApiProperty({
    description: 'Specifies granted permissions',
  })
  @IsObject()
  @Expose()
  permissions: Permissions;
}

export class AppUser {
  @ApiProperty()
  @Expose()
  @IsNumber()
  id: number;

  @ApiPropertyOptional()
  @Expose()
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  language?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  dateOfBirth?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  signUpCountry?: string;

  @ApiProperty({ default: false })
  @Expose()
  @IsBoolean()
  isDeleted?: boolean = false;

  @ApiProperty()
  @Expose()
  @IsString()
  parentEmail?: string;

  @ApiPropertyOptional({ default: false })
  @Expose()
  @IsBoolean()
  parentVerified?: boolean = false;

  @ApiPropertyOptional({ default: false })
  @Expose()
  @IsBoolean()
  parentExpired?: boolean = false;

  @ApiPropertyOptional({ default: false })
  @Expose()
  @IsBoolean()
  parentRejected?: boolean = false;

  @ApiPropertyOptional({ default: false })
  @Expose()
  @IsBoolean()
  parentIdVerified?: boolean = false;

  @ApiPropertyOptional({ default: false })
  @Expose()
  @IsBoolean()
  parentDeleted?: boolean = false;
}

export class ResultPermission {
  @ApiProperty()
  @IsString()
  @Expose()
  displayName: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Expose()
  description?: string;

  @ApiProperty()
  @IsString()
  @Expose()
  name: string;
}
export class ResultPermissions {
  @ApiProperty({
    description: 'List of permissions requiring consent',
    type: ResultPermission,
    isArray: true,
  })
  @Expose()
  @Type(() => ResultPermission)
  missingPermissions: ResultPermission[];

  @ApiProperty({
    description: 'List of permissions assigned by default',
    type: ResultPermission,
    isArray: true,
  })
  @Expose()
  automaticallyGrantedPerms: ResultPermission[];

  @ApiProperty({
    description: 'List of permissions that have already been requested previously',
    type: ResultPermission,
    isArray: true,
  })
  @Expose()
  @Type(() => ResultPermission)
  alreadyGrantedPerms: ResultPermission[];
}

export class AppUserParams {
  @ApiProperty({ description: 'Application ID' })
  @IsNumber()
  @Type(() => Number)
  appId: number;

  @ApiProperty({ description: 'User ID' })
  @IsNumber()
  @Type(() => Number)
  userId: number;
}

export class RequestUserPermissionsDTO {
  @ApiPropertyOptional({
    description: "User's parent email. Required if user age is under consent age for the country.",
  })
  @IsEmail()
  @IsOptional()
  parentEmail?: string;

  @ApiPropertyOptional({
    description: `Date of birth of the user in YYYY-MM-DD format.`,
    example: '2010-10-10',
  })
  @Matches(DATE_FORMAT_REGEX)
  @IsOptional()
  dateOfBirth?: string;

  @ApiProperty({
    description: 'Array of permissions for which consent will be requested.',
  })
  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  permissions?: string[];
}

export class RequestUserPermissionsResponseDTO {
  @ApiProperty()
  @Type(() => AppUser)
  @Expose()
  user: AppUser;

  @ApiProperty({
    type: 'object',
    additionalProperties: {
      type: 'boolean',
      enum: [true, 'null'],
      nullable: true,
    },
  })
  @IsObject()
  @Expose()
  permissions: Permissions;

  @ApiPropertyOptional()
  @Expose()
  @IsOptional()
  @Type(() => ResultPermissions)
  resultPermissions?: ResultPermissions;
}

export class UpdateParentEmailDTO {
  @ApiProperty()
  @IsEmail()
  parentEmail: string;
}

export class UpdateUserDateOfBirthDTO {
  @ApiProperty({
    description: `Date of birth of the user in YYYY-MM-DD format.`,
    example: '2010-10-10',
  })
  @Matches(DATE_FORMAT_REGEX)
  dateOfBirth: string;

  @IsString()
  @ApiPropertyOptional({
    example: 'US',
    description: 'The location of the family member',
  })
  location: Tiso31662;
}

export class ParentState {
  @ApiProperty()
  @Expose()
  verified: boolean;

  @ApiProperty()
  @Expose()
  expired: boolean;

  @ApiProperty()
  @Expose()
  rejected: boolean;

  @ApiProperty()
  @Expose()
  idVerified: boolean;

  @ApiProperty()
  @Expose()
  deleted: boolean;
}

export class ParentDetail {
  @ApiProperty()
  @Expose()
  oauthProvider?: string | null;

  @ApiProperty()
  @Expose()
  usedVerificationMethodName?: string | null;
}

export class GetUserResponseDTO {
  @ApiProperty()
  @Expose()
  id: number;

  @ApiPropertyOptional()
  @Expose()
  dateOfBirth?: Date;

  @ApiProperty()
  @Expose()
  language: string;

  @ApiProperty()
  @Expose()
  permissions: Permissions;

  @ApiPropertyOptional()
  @Expose()
  username?: null = null;

  @ApiPropertyOptional()
  @Expose()
  displayName?: null = null;

  @ApiPropertyOptional()
  @Expose()
  appData?: string[] = []; // Not sure if this is right, appData is an array of _something_

  @ApiProperty()
  @Expose()
  @IsDate()
  @Transform(({ value }: { value: Date }) => value?.toISOString())
  activationCreatedAt: Date;

  @ApiProperty()
  @Expose()
  @IsDate()
  @Transform(({ value }: { value: Date }) => value?.toISOString())
  createdAt: Date;

  @ApiProperty()
  @Expose()
  consentAgeForCountry: number;

  @ApiProperty()
  @Expose()
  isMinor: boolean;

  @ApiPropertyOptional()
  @Expose()
  parentEmail?: string | undefined;

  @ApiPropertyOptional()
  @Expose()
  parentState?: ParentState;

  @ApiPropertyOptional()
  @Expose()
  streetAddress?: null = null;

  @ApiPropertyOptional()
  @Expose()
  postalCode?: null = null;

  @ApiPropertyOptional()
  @Expose()
  firstName?: null = null;

  @ApiPropertyOptional()
  @Expose()
  lastName?: null = null;

  @ApiPropertyOptional()
  @Expose()
  email?: null = null;

  @ApiPropertyOptional()
  @Expose()
  country?: null = null;

  @ApiPropertyOptional()
  @Expose()
  city?: null = null;

  @ApiPropertyOptional()
  @Expose()
  parentDetails?: ParentDetail;
}
