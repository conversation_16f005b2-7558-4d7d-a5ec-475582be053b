import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { App } from './app.entity';

@Entity()
export class AppTranslation {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @PrimaryColumn({ type: 'character varying', name: 'orgEnvId' })
  orgEnvId: string;

  @ManyToOne(() => App, (app) => app.translations, { onDelete: 'CASCADE', nullable: false })
  @JoinColumn([
    { name: 'appId', referencedColumnName: 'id' },
    { name: 'orgEnvId', referencedColumnName: 'orgEnvId' },
  ])
  app: App;

  @Column()
  language: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  termsAndConditionsUrl?: string;

  @Column({ nullable: true })
  privacyPolicyUrl?: string;

  @Column({ nullable: true })
  logoUrl?: string;

  @Column({ nullable: true })
  iconUrl?: string;

  @Column({ nullable: true })
  headerLogoUrl?: string;

  @Column({ nullable: true })
  faviconUrl?: string;

  @Column({ nullable: true })
  splashScreenLogoUrl?: string;

  @Column({ nullable: true })
  splashScreenBgImageUrl?: string;

  @Column({ nullable: true })
  mainContainerBgImageUrl?: string;

  @Column({ nullable: true })
  mainContainerBgImageFill?: string;

  @Column({ nullable: true })
  brandingEmailHeaderBgImg?: string;

  @Column({ nullable: true })
  emailFooterCopy?: string;

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;
}
