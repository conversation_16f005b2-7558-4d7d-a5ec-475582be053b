import { ApiExtraModels, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Permissions } from '@superawesome/freekws-classic-wrapper-common';
import { Expose, Transform } from 'class-transformer';
import { IsArray, IsBoolean, IsObject, IsOptional, IsString } from 'class-validator';

@ApiExtraModels()
export class GetUserPermissionsResponseDTO {
  [key: string]: boolean | null;

  static getSchema() {
    return {
      type: 'object',
      additionalProperties: {
        type: ['boolean', 'null'],
      },
      example: {
        'chat.voice': true,
        'chat.send': false,
        'chat.moderate': null,
      },
    };
  }
}

@ApiExtraModels()
export class GetUserPermissionsResponseExtendedDTO {
  @ApiProperty({
    description: 'Map of permission keys to their boolean values',
    example: {
      'chat.voice': true,
      'chat.text': false,
    },
    type: 'object',
    additionalProperties: {
      type: 'boolean',
      nullable: true,
    },
  })
  @IsObject()
  @Expose()
  permissions: Permissions;

  @IsBoolean()
  @IsOptional()
  @Expose()
  @ApiPropertyOptional({
    description: 'Indicates if the user has graduated from minor status',
    example: false,
    default: undefined,
    type: Boolean,
  })
  isGraduated?: boolean = undefined;

  @IsArray()
  @Expose()
  @IsOptional()
  @ApiPropertyOptional({
    description: 'DEPRECATED: This field is deprecated and will always return an empty array',
    default: [],
    deprecated: true,
  })
  disabledPermissions: string[] = [];
}

export class AppsUsersActivateInputDTO {
  @ApiProperty({
    description: 'Permissions to request, if desired, in the form of an array of permission identifiers.',
    example: ['read_profile', 'send_messages'],
    type: [String],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => value ?? [])
  permissions?: string[];
}
