import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';

import { AppTranslation } from './app-translation.entity';
import { OrgEnv } from '../org-env/org-env.entity';
import { Activation } from '../user/activation.entity';

// The migrations needed to be created manually. THere are issues applying the pseudorandom id generators that
// keep creating new migrations all the time (lots of things were tried like adding a default for the column that
// invokes the pseudorandom function etc, but it ended up always with issues)
// Future migrations should be created manually
@Entity({ synchronize: false })
@Index('app_oauthclient_orgenv_idx', ['oauthClientId', 'orgEnvId'], { unique: true })
@Unique(['orgEnvId', 'productId'])
export class App {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => OrgEnv, (env) => env.apps, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'orgEnvId' })
  orgEnv: OrgEnv;

  @PrimaryColumn({ type: 'character varying', name: 'orgEnvId' })
  orgEnvId: string;

  @Column()
  productId: string;

  @Column()
  productEnvId: string;

  @Column()
  name: string;

  @Column()
  mode: string;

  @Column()
  oauthClientId: string;

  @Column({ nullable: true })
  oauthCallback?: string;

  @Column()
  apiKey: string;

  @Column()
  mobileApiKey: string;

  @Column({ default: false })
  termsAndConditionsRequired: boolean;

  @OneToMany(() => AppTranslation, (translation: AppTranslation) => translation.app)
  @JoinColumn([
    { name: 'id', referencedColumnName: 'appId' },
    { name: 'orgEnvId', referencedColumnName: 'orgEnvId' },
  ])
  translations: AppTranslation[];

  @OneToMany(() => Activation, (activation) => activation.app)
  @JoinColumn([
    { name: 'id', referencedColumnName: 'appId' },
    { name: 'orgEnvId', referencedColumnName: 'orgEnvId' },
  ])
  activations: Activation[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
