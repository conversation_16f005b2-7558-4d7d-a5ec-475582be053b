import { BadRequestException, ConflictException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  ESettingBooleanOrder,
  ESettingConsentType,
  ESettingValueType,
  EUserSettingValueEffectiveSource,
  UserSettingValueDTO,
} from '@superawesome/freekws-settings-common';
import { AxiosError } from 'axios';
import { Repository, TypeORMError } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { App } from './app.entity';
import { AppService } from './app.service';
import { IAppInfo, SettingsConfiguration } from './types';
import { AgeGateService } from '../common/services/age-gate/age-gate.service';
import { AnalyticService } from '../common/services/analytic/analytic.service';
import { SettingsService } from '../common/services/settings/settings.service';
import {
  SettingsServiceConsentNotRequestedError,
  SettingsServiceParentMissingError,
  SettingsServiceParentNotInFamilyError,
} from '../common/services/settings/types';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { Activation } from '../user/activation.entity';
import { User } from '../user/user.entity';
import { UserService } from '../user/user.service';

const optInDefinition = {
  ageBracket: {
    consentType: ESettingConsentType.OPT_IN_UNVERIFIED,
  },
};

const orgEnvStub = {
  id: uuidv4(),
  clientId: 'client-id',
  clientSecret: 'top-secret',
} as OrgEnv;
const appStubWithOrg = {
  id: 123456,
  orgEnv: orgEnvStub,
  orgEnvId: orgEnvStub.id,
  productEnvId: '',
  productId: 'product-id',
  name: '',
  mode: '',
  oauthClientId: 'client-id',
  apiKey: 'top-secret',
  mobileApiKey: 'blue-shell',
  translations: [],
  activations: [],
  termsAndConditionsRequired: false,
} as unknown as App;
const appStub = {
  id: 123456,
  orgEnv: orgEnvStub.id as unknown as OrgEnv,
  orgEnvId: orgEnvStub.id,
  productEnvId: '',
  productId: '',
  name: '',
  mode: '',
  oauthClientId: 'client-id',
  apiKey: 'top-secret',
  mobileApiKey: 'red-ring',
  translations: [],
  activations: [],
  termsAndConditionsRequired: false,
} as unknown as App;

describe('AppService', () => {
  let service: AppService;
  let appRepo: Repository<App>;
  let ageGateService: AgeGateService;
  let userService: UserService;
  let settingsService: SettingsService;
  let analyticService: AnalyticService;

  beforeEach(async () => {
    const module = await Testing.createModule({
      providers: [AppService],
    });

    service = module.get<AppService>(AppService);
    appRepo = module.get(getRepositoryToken(App));
    ageGateService = module.get(AgeGateService);
    userService = module.get(UserService);
    settingsService = module.get(SettingsService);
    analyticService = module.get(AnalyticService);

    jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValueOnce(appStubWithOrg);
  });

  function mockUserAndAgeOfConsent() {
    jest.spyOn(userService, 'getById').mockResolvedValueOnce({
      id: 1,
      signUpCountry: 'GB',
    } as User);
    jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValueOnce({
      country: 'US',
      consentAge: 16,
      underAgeOfDigitalConsent: true,
    });
  }

  describe('getAppOauthClient', () => {
    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should return oauth client params', async () => {
      jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValueOnce(appStub);

      await expect(
        service.getAppOauthClient(
          {
            clientId: 'client-id',
            secret: 'top-secret',
          },
          orgEnvStub.id,
        ),
      ).resolves.toEqual({
        appId: 123456,
        clientId: 'client-id',
        orgEnvId: orgEnvStub.id,
      });
    });

    it('should throw an error if client not found', async () => {
      const findOneOrFail = jest.spyOn(appRepo, 'findOneOrFail');
      findOneOrFail.mockReset();
      findOneOrFail.mockRejectedValueOnce(new TypeORMError('Not Found'));

      await expect(
        service.getAppOauthClient(
          {
            clientId: 'client-id',
            secret: 'top-secret',
          },
          orgEnvStub.id,
        ),
      ).rejects.toThrow('Not Found');
    });
  });

  describe('registerUser', () => {
    it('should create user', async () => {
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: true });
      jest
        .spyOn(userService, 'create')
        .mockResolvedValueOnce({ id: 1, uuid: '00000000-0000-0000-0000-000000000000', language: 'en' } as User);
      jest.spyOn(settingsService, 'sendConsentEmail').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'voice',
          preferredValue: true,
          preferredValueFromOrgLevel: false,
          effectiveValue: true,
          effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
          isOrgLevel: false,
        },
      ]);
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          definition: optInDefinition,
        },
      ] as UserSettingValueDTO[]);
      const spyAnalytic = jest.spyOn(analyticService, 'signupSuccess');

      await expect(
        service.registerUser(
          orgEnvStub.id,
          {
            country: 'US',
            dateOfBirth: '2014-10-10',
            language: 'id',
            parentEmail: '<EMAIL>',
            permissions: ['chat.voice'],
          },
          1,
        ),
      ).resolves.toEqual({
        id: 1,
        isMinor: true,
        permissions: {
          'chat.voice': null,
        },
        uuid: '00000000-0000-0000-0000-000000000000',
      });
      expect(spyAnalytic).toHaveBeenNthCalledWith(1, 'US', 1, undefined, undefined, { language: 'en' });
    });

    it('creates who is not a minor user without consent email', async () => {
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: false });
      jest
        .spyOn(userService, 'create')
        .mockResolvedValueOnce({ id: 1, uuid: '00000000-0000-0000-0000-000000000000', language: 'en' } as User);
      const consentSpy = jest.spyOn(settingsService, 'sendConsentEmail');
      const spyAnalytic = jest.spyOn(analyticService, 'signupSuccess');

      await expect(
        service.registerUser(
          orgEnvStub.id,
          {
            country: 'US',
            dateOfBirth: '2004-10-10',
            language: 'id',
            parentEmail: '<EMAIL>',
            permissions: ['chat.voice'],
          },
          1,
        ),
      ).resolves.toEqual({
        id: 1,
        isMinor: false,
        permissions: {
          'chat.voice': true,
        },
        uuid: '00000000-0000-0000-0000-000000000000',
      });
      expect(spyAnalytic).toHaveBeenNthCalledWith(1, 'US', 1, undefined, undefined, { language: 'en' });
      expect(consentSpy).not.toHaveBeenCalled();
    });

    it('should throw an error if paren email not presented', async () => {
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: true });
      jest
        .spyOn(userService, 'create')
        .mockResolvedValueOnce({ id: 1, uuid: '00000000-0000-0000-0000-000000000000', language: 'en' } as User);
      jest.spyOn(settingsService, 'sendConsentEmail').mockRejectedValueOnce(new SettingsServiceParentMissingError());
      const spyAnalytic = jest.spyOn(analyticService, 'signupSuccess');

      await expect(
        service.registerUser(
          orgEnvStub.id,
          {
            country: 'US',
            dateOfBirth: '2014-10-10',
            language: 'id',
            parentEmail: '<EMAIL>',
            permissions: ['chat.voice'],
          },
          1,
        ),
      ).rejects.toBeInstanceOf(BadRequestException);
      expect(spyAnalytic).not.toHaveBeenCalled();
    });

    it('should throw an error if consent request failed', async () => {
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: true });
      jest
        .spyOn(userService, 'create')
        .mockResolvedValueOnce({ id: 1, uuid: '00000000-0000-0000-0000-000000000000', language: 'en' } as User);
      jest.spyOn(settingsService, 'sendConsentEmail').mockRejectedValueOnce(new AxiosError('ECONRESET'));
      const spyAnalytic = jest.spyOn(analyticService, 'signupSuccess');

      await expect(
        service.registerUser(
          orgEnvStub.id,
          {
            country: 'US',
            dateOfBirth: '2014-10-10',
            language: 'id',
            parentEmail: '<EMAIL>',
            permissions: ['chat.voice'],
          },
          1,
        ),
      ).rejects.toThrow('ECONRESET');
      expect(spyAnalytic).not.toHaveBeenCalled();
    });
  });

  describe('requestPermissionsForUser', () => {
    it('should throw if user not found', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce(null);
      await expect(
        service.requestPermissionsForUser(orgEnvStub.id, {
          userId: 1,
          appId: 2,
          permissions: [],
        }),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw if user over AODC', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        dateOfBirth: new Date('2010-10-10'),
        signUpCountry: 'GB',
      } as User);
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: false });
      await expect(
        service.requestPermissionsForUser(orgEnvStub.id, {
          userId: 1,
          appId: 2,
          permissions: [],
        }),
      ).rejects.toBeInstanceOf(ForbiddenException);
    });

    it('should return user if permissions not presented', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        dateOfBirth: new Date('2010-10-10'),
        signUpCountry: 'GB',
      } as User);
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: true });
      jest.spyOn(userService, 'getUserActivation').mockResolvedValueOnce({} as Activation);
      await expect(
        service.requestPermissionsForUser(orgEnvStub.id, {
          userId: 1,
          appId: 2,
          permissions: [],
        }),
      ).resolves.toEqual({
        user: { id: 1, dateOfBirth: '2010-10-10', signUpCountry: 'GB' },
        userSettings: null,
      });
    });

    it('should throw if parent email not found', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: true });
      jest.spyOn(settingsService, 'sendConsentEmail').mockRejectedValueOnce(new SettingsServiceParentMissingError());
      jest.spyOn(userService, 'getUserActivation').mockResolvedValueOnce({} as Activation);
      await expect(
        service.requestPermissionsForUser(orgEnvStub.id, {
          userId: 1,
          appId: 2,
          dateOfBirth: '2010-10-10',
          permissions: ['chat.voice'],
        }),
      ).rejects.toBeInstanceOf(BadRequestException);
    });

    it('should throw if consent request failed', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: true });
      jest.spyOn(settingsService, 'sendConsentEmail').mockRejectedValueOnce(new AxiosError('ECONRESET'));
      jest.spyOn(userService, 'getUserActivation').mockResolvedValueOnce({} as Activation);

      await expect(
        service.requestPermissionsForUser(orgEnvStub.id, {
          userId: 1,
          appId: 2,
          dateOfBirth: '2010-10-10',
          permissions: ['chat.voice'],
        }),
      ).rejects.toThrow('ECONRESET');
    });

    const settingDefinition = {
      ageBracket: {
        consentType: ESettingConsentType.OPT_OUT,
        defaultPreference: '',
      },
      orgId: '',
      namespace: '',
      settingName: '',
      valueType: ESettingValueType.BOOLEAN,
      translations: {
        en: {
          label: 'Voice chat',
          parentNotice: 'Description here',
        },
      },
      restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
      userHidden: false,
      userReadOnly: false,
      required: false,
    };

    it('should add T&C permission when termsAndConditionsRequired is true and user has not accepted T&C', async () => {
      const appWithTandC = {
        ...appStubWithOrg,
        id: 111111111,
        termsAndConditionsRequired: true,
      };

      jest.spyOn(appRepo, 'findOneOrFail').mockReset();
      jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValueOnce(appWithTandC);
      mockUserAndAgeOfConsent();

      jest.spyOn(userService, 'getUserActivation').mockResolvedValueOnce({} as Activation);
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          definition: settingDefinition,
        } as unknown as UserSettingValueDTO,
      ]);

      const sendConsentEmailSpy = jest.spyOn(settingsService, 'sendConsentEmail').mockResolvedValueOnce([]);

      await service.requestPermissionsForUser(orgEnvStub.id, {
        userId: 1,
        appId: 2,
        dateOfBirth: '2010-10-10',
        permissions: ['chat.voice'],
      });

      expect(sendConsentEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          permissions: expect.arrayContaining(['terms.app-terms-and-conditions', 'chat.voice']),
        }),
        expect.anything(),
      );
    });

    it('should not add T&C permission when termsAndConditionsRequired is true but user has already accepted T&C', async () => {
      const appWithTandC = {
        ...appStubWithOrg,
        termsAndConditionsRequired: true,
      };

      jest.spyOn(appRepo, 'findOneOrFail').mockReset();
      jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValueOnce(appWithTandC);
      jest.spyOn(userService, 'getUserActivation').mockResolvedValueOnce({} as Activation);

      mockUserAndAgeOfConsent();

      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'terms',
          settingName: 'app-terms-and-conditions',
          effectiveValue: true,
          definition: settingDefinition,
        } as unknown as UserSettingValueDTO,
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          definition: settingDefinition,
        } as unknown as UserSettingValueDTO,
      ]);

      const sendConsentEmailSpy = jest.spyOn(settingsService, 'sendConsentEmail').mockResolvedValueOnce([]);

      await service.requestPermissionsForUser(orgEnvStub.id, {
        userId: 1,
        appId: 2,
        dateOfBirth: '2010-10-10',
        permissions: ['chat.voice'],
      });

      expect(sendConsentEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          permissions: ['chat.voice'],
        }),
        expect.anything(),
      );
    });

    it('should not add T&C permission when termsAndConditionsRequired is false', async () => {
      const appWithoutTandC = {
        ...appStubWithOrg,
        termsAndConditionsRequired: false,
      };

      jest.spyOn(appRepo, 'findOneOrFail').mockReset();
      jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValueOnce(appWithoutTandC);
      jest.spyOn(userService, 'getUserActivation').mockResolvedValueOnce({} as Activation);

      mockUserAndAgeOfConsent();

      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          definition: settingDefinition,
        } as unknown as UserSettingValueDTO,
      ]);

      const sendConsentEmailSpy = jest.spyOn(settingsService, 'sendConsentEmail').mockResolvedValueOnce([]);

      await service.requestPermissionsForUser(orgEnvStub.id, {
        userId: 1,
        appId: 2,
        dateOfBirth: '2010-10-10',
        permissions: ['chat.voice'],
      });

      expect(sendConsentEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          permissions: ['chat.voice'],
        }),
        expect.anything(),
      );
    });
  });

  describe('updateParentEmail', () => {
    it('should successfully resolves', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
        dateOfBirth: new Date('2020-10-10'),
      } as User);
      jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValueOnce({
        country: 'GB',
        consentAge: 16,
        underAgeOfDigitalConsent: true,
      });

      await expect(
        service.updateParentEmail(orgEnvStub.id, { appId: 1, userId: 1, parentEmail: 'email' }),
      ).resolves.toBeUndefined();
    });

    it('should successfully resolves for user without dob', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);

      await expect(
        service.updateParentEmail(orgEnvStub.id, { appId: 1, userId: 1, parentEmail: 'email' }),
      ).resolves.toBeUndefined();
    });

    it('should throw an error for unknown user', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce(null);

      await expect(
        service.updateParentEmail(orgEnvStub.id, { appId: 1, userId: 1, parentEmail: 'email' }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw an error for user with verified parent', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);
      jest
        .spyOn(settingsService, 'resendConsentEmail')
        .mockRejectedValueOnce(new SettingsServiceParentNotInFamilyError());

      await expect(
        service.updateParentEmail(orgEnvStub.id, { appId: 1, userId: 1, parentEmail: 'email' }),
      ).rejects.toThrow(ConflictException);
    });

    it('should throw an error for graduated user', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
        dateOfBirth: new Date('2010-10-10'),
      } as User);
      jest
        .spyOn(settingsService, 'resendConsentEmail')
        .mockRejectedValueOnce(new SettingsServiceConsentNotRequestedError());

      await expect(
        service.updateParentEmail(orgEnvStub.id, { appId: 1, userId: 1, parentEmail: 'email' }),
      ).rejects.toThrow(ConflictException);
    });

    it('should throw an error for failed settings request', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
        dateOfBirth: new Date('2010-10-10'),
      } as User);
      jest.spyOn(settingsService, 'resendConsentEmail').mockRejectedValueOnce(new AxiosError());

      await expect(
        service.updateParentEmail(orgEnvStub.id, { appId: 1, userId: 1, parentEmail: 'email' }),
      ).rejects.toThrow(AxiosError);
    });
  });

  describe('getUser', () => {
    const settingsMock = [
      {
        namespace: 'chat',
        settingName: 'voice',
        preferredValue: true,
        preferredValueFromOrgLevel: false,
        effectiveValue: true,
        isOrgLevel: false,
        effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
        definition: {
          ageBracket: {
            consentType: ESettingConsentType.OPT_OUT,
            defaultPreference: '',
          },
          orgId: '',
          namespace: '',
          settingName: '',
          valueType: ESettingValueType.BOOLEAN,
          translations: {
            en: {
              label: 'Voice chat',
              parentNotice: 'Description here',
            },
          },
          restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
          userHidden: false,
          userReadOnly: false,
          required: false,
        },
      },
    ];

    it('should return user info for verified parent', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
        dateOfBirth: new Date('2020-10-10'),
        createdAt: new Date('2024-12-12'),
      } as User);
      jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValueOnce({ country: 'US', consentAge: 16 });
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce(settingsMock);
      jest.spyOn(userService, 'getParentEmail').mockResolvedValueOnce('<EMAIL>');
      jest.spyOn(userService, 'getParentStateAllFalse').mockReturnValueOnce({
        verified: false,
        idVerified: false,
        deleted: false,
        rejected: false,
        expired: false,
      });

      await expect(service.getUser(orgEnvStub.id, { userId: 1, appId: 1 })).resolves.toEqual({
        activationCreatedAt: new Date('2024-12-12'),
        consentAgeForCountry: 16,
        createdAt: new Date('2024-12-12'),
        dateOfBirth: new Date('2020-10-10'),
        displayName: null,
        id: 1,
        isMinor: true,
        language: undefined,
        parentEmail: '<EMAIL>',
        parentState: {
          deleted: false,
          expired: false,
          idVerified: false,
          rejected: false,
          verified: false,
        },
        permissions: {
          'chat.voice': null,
        },
        username: null,
        parentDetails: {
          oauthProvider: null,
          usedVerificationMethodName: 'KWS',
        },
      });
    });

    it('should return user info for control panel scope and unverified parent', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
        dateOfBirth: new Date('2020-10-10'),
        createdAt: new Date('2024-12-12'),
      } as User);
      jest
        .spyOn(ageGateService, 'getConsentAgeForCountry')
        .mockResolvedValueOnce({ country: 'US', consentAge: 16, underAgeOfDigitalConsent: true });
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce(settingsMock);
      jest.spyOn(userService, 'getParentEmail').mockResolvedValueOnce(void 0);

      await expect(service.getUser(orgEnvStub.id, { userId: 1, appId: 1 })).resolves.toEqual({
        activationCreatedAt: new Date('2024-12-12'),
        createdAt: new Date('2024-12-12'),
        consentAgeForCountry: 16,
        dateOfBirth: new Date('2020-10-10'),
        displayName: null,
        language: undefined,
        id: 1,
        isMinor: true,
        permissions: {
          'chat.voice': null,
        },
        username: null,
        parentDetails: {
          oauthProvider: null,
          usedVerificationMethodName: 'KWS',
        },
      });
    });

    it('should return info for user without DOB for control panel', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
        createdAt: new Date('2024-12-12'),
      } as User);
      jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValueOnce({ country: 'US', consentAge: 16 });
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce(settingsMock);
      jest.spyOn(userService, 'getParentEmail').mockResolvedValueOnce(void 0);

      await expect(service.getUser(orgEnvStub.id, { userId: 1, appId: 1 })).resolves.toEqual({
        activationCreatedAt: new Date('2024-12-12'),
        createdAt: new Date('2024-12-12'),
        consentAgeForCountry: 16,
        dateOfBirth: undefined,
        language: undefined,
        displayName: null,
        id: 1,
        isMinor: true,
        permissions: {
          'chat.voice': null,
        },
        username: null,
        parentDetails: {
          oauthProvider: null,
          usedVerificationMethodName: 'KWS',
        },
      });
    });

    it('should return info for user without DOB for app', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
        createdAt: new Date('2024-12-12'),
      } as User);
      jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValueOnce({ country: 'US', consentAge: 16 });
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce(settingsMock);
      jest.spyOn(userService, 'getParentEmail').mockResolvedValueOnce(void 0);

      await expect(service.getUser(orgEnvStub.id, { userId: 1, appId: 1 })).resolves.toEqual({
        activationCreatedAt: new Date('2024-12-12'),
        createdAt: new Date('2024-12-12'),
        consentAgeForCountry: 16,
        displayName: null,
        datOfBirth: undefined,
        language: undefined,
        id: 1,
        isMinor: true,
        permissions: {
          'chat.voice': null,
        },
        username: null,
        parentDetails: {
          oauthProvider: null,
          usedVerificationMethodName: 'KWS',
        },
      });
    });

    it('should throw an error if user not found', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce(null);

      await expect(service.getUser(orgEnvStub.id, { userId: 1, appId: 1 })).rejects.toThrow(BadRequestException);
    });
  });

  describe('userHasActivation', () => {
    it('returns true when user has activation for app', async () => {
      jest.spyOn(userService, 'getUserActivation').mockResolvedValue({ id: 1 } as Activation);

      const result = await service.userHasActivation(orgEnvStub.id, 1, 2);

      expect(userService.getUserActivation).toHaveBeenCalledWith(orgEnvStub.id, 1, 2);
      expect(result).toBe(true);
    });

    it('returns false when user has no activation for app', async () => {
      jest.spyOn(userService, 'getUserActivation').mockResolvedValue(null);

      const result = await service.userHasActivation(orgEnvStub.id, 1, 2);

      expect(userService.getUserActivation).toHaveBeenCalledWith(orgEnvStub.id, 1, 2);
      expect(result).toBe(false);
    });
  });

  describe('getAppInfoByUser', () => {
    it('should return user info for verified parent', async () => {
      await expect(service.getAppInfoByUser(orgEnvStub.id, 1)).resolves.toEqual({
        credentials: {
          clientId: 'client-id',
          secret: 'top-secret',
        },
        productId: 'product-id',
      });
    });
  });

  describe('reviewPermissions', () => {
    it('should throw if user not found', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce(null);

      await expect(service.reviewPermissions(1, 2, orgEnvStub.id)).rejects.toThrow(NotFoundException);
    });

    it('should send consent email with previously requested settings', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);

      const mockSettings = [
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          consentRequestedAt: Date.now(),
        },
        {
          namespace: 'privacy',
          settingName: 'sharing',
          effectiveValue: false,
          // No consentRequestedAt, this one shouldn't be included
        },
      ] as UserSettingValueDTO[];

      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce(mockSettings);

      const sendConsentEmailSpy = jest.spyOn(settingsService, 'sendConsentEmail').mockResolvedValueOnce([]);

      await service.reviewPermissions(1, 2, orgEnvStub.id);

      expect(sendConsentEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 1,
          productId: 'product-id',
          settings: [mockSettings[0]], // Only the setting with consentRequestedAt
        }),
        expect.anything(),
      );
    });

    it('should handle parent missing error', async () => {
      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);

      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          consentRequestedAt: Date.now(),
        },
      ] as UserSettingValueDTO[]);

      jest.spyOn(settingsService, 'sendConsentEmail').mockRejectedValueOnce(new SettingsServiceParentMissingError());

      await expect(service.reviewPermissions(1, 2, orgEnvStub.id)).rejects.toThrow(BadRequestException);
    });

    it('should return early if no settings have been previously requested', async () => {
      jest.spyOn(service, 'getAppInfo').mockResolvedValueOnce({
        productId: 'product-id',
        credentials: { clientId: 'client-id', secret: 'client-secret' },
      } as IAppInfo);

      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);

      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          consentRequestedAt: undefined,
        } as UserSettingValueDTO,
      ]);

      const sendConsentEmailSpy = jest.spyOn(settingsService, 'sendConsentEmail');

      await service.reviewPermissions(1, 2, orgEnvStub.id);

      expect(sendConsentEmailSpy).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when parent email is missing', async () => {
      jest.spyOn(service, 'getAppInfo').mockResolvedValueOnce({
        productId: 'product-id',
        credentials: { clientId: 'client-id', secret: 'client-secret' },
      } as IAppInfo);

      jest.spyOn(userService, 'getById').mockResolvedValueOnce({
        id: 1,
        signUpCountry: 'GB',
      } as User);

      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          consentRequestedAt: Date.now(),
        } as UserSettingValueDTO,
      ]);

      jest.spyOn(settingsService, 'sendConsentEmail').mockRejectedValueOnce(new SettingsServiceParentMissingError());

      await expect(service.reviewPermissions(1, 2, orgEnvStub.id)).rejects.toThrow(BadRequestException);

      expect(settingsService.sendConsentEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 1,
          productId: 'product-id',
          settings: expect.arrayContaining([
            expect.objectContaining({
              namespace: 'chat',
              settingName: 'voice',
              consentRequestedAt: expect.any(Number),
            }),
          ]),
        }),
        expect.anything(),
      );
    });
  });

  describe('getTranslatedPermissionsForApp', () => {
    const mockAppId = 123;
    const mockOrgEnv = { id: orgEnvStub.id, orgId: 'test-org-id' };
    const mockSettingsConfigurations: SettingsConfiguration[] = [
      {
        version: 1,
        orgId: 'test-org-id',
        productId: 'product-id',
        namespace: 'chat',
        settings: [
          {
            settingName: 'voice',
            valueType: 'boolean',
            userHidden: false,
            required: false,
            autoReviewConsent: false,
            label: { en: 'Voice Chat', es: 'Chat de Voz' },
            parentNotice: { en: 'Allow voice chat\n\nDetailed notice', es: 'Permitir chat de voz' },
            userNotice: { en: 'Voice chat permission', es: 'Permiso de chat de voz' },
            regions: [],
          },
          {
            settingName: 'text',
            valueType: 'boolean',
            userHidden: false,
            required: false,
            autoReviewConsent: false,
            label: { en: 'Text Chat' },
            parentNotice: { en: 'Allow text chat' },
            userNotice: { en: 'Text chat permission' },
            regions: [],
          },
        ],
      },
      {
        version: 1,
        orgId: 'test-org-id',
        productId: 'product-id',
        namespace: 'terms',
        settings: [
          {
            settingName: 'app-terms-and-conditions',
            valueType: 'boolean',
            userHidden: false,
            required: false,
            autoReviewConsent: false,
            label: { en: 'Terms and Conditions' },
            parentNotice: { en: 'Accept terms and conditions' },
            userNotice: { en: 'Terms and conditions' },
            regions: [],
          },
        ],
      },
    ];

    beforeEach(() => {
      jest.spyOn(service, 'getAppInfoBy').mockResolvedValue({
        app: appStubWithOrg,
        productId: 'product-id',
        credentials: { clientId: 'client-id', secret: 'top-secret' },
      } as IAppInfo);
      jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValue(mockSettingsConfigurations);
    });

    it('should return all translated permissions when no specific permissions requested, excluding terms and conditions', async () => {
      const result = await service.getTranslatedPermissionsForApp(
          mockAppId,
          mockOrgEnv,
          'US',
          'en',
          []
      );

      expect(result).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Voice Chat',
          childFacingDescription: 'Voice chat permission',
          privacyNotice: 'Detailed notice',
        },
        {
          name: 'chat.text',
          displayName: 'Text Chat',
          childFacingDescription: 'Text chat permission',
          privacyNotice: 'Allow text chat',
        },
      ]);

      expect(service.getAppInfoBy).toHaveBeenCalledWith(mockAppId, mockOrgEnv.id);
      expect(settingsService.getProductSettingsDefinition).toHaveBeenCalledWith({
        productId: appStubWithOrg.productId,
        productEnvId: appStubWithOrg.productEnvId,
        orgId: mockOrgEnv.orgId,
        orgEnvId: mockOrgEnv.id,
      });
    });

    it('should filter permissions when specific permissions are requested', async () => {
      const result = await service.getTranslatedPermissionsForApp(
          mockAppId,
          mockOrgEnv,
          'US',
          'en',
          ['chat.voice']
      );

      expect(result).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Voice Chat',
          childFacingDescription: 'Voice chat permission',
          privacyNotice: 'Detailed notice',
        },
      ]);
    });

    it('should use Spanish translations when accept-language is es', async () => {
      const result = await service.getTranslatedPermissionsForApp(
          mockAppId,
          mockOrgEnv,
          'US',
          'es',
          ['chat.voice']
      );

      expect(result).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Chat de Voz',
          childFacingDescription: 'Permiso de chat de voz',
          privacyNotice: 'Permitir chat de voz',
        },
      ]);
    });

    it('should fall back to English when requested language is not available', async () => {
      const result = await service.getTranslatedPermissionsForApp(
          mockAppId,
          mockOrgEnv,
          'US',
          'fr',
          ['chat.text']
      );

      expect(result).toEqual([
        {
          name: 'chat.text',
          displayName: 'Text Chat',
          childFacingDescription: 'Text chat permission',
          privacyNotice: 'Allow text chat',
        },
      ]);
    });

    it('should use English when no accept-language is provided', async () => {
      const result = await service.getTranslatedPermissionsForApp(
          mockAppId,
          mockOrgEnv,
          'US',
          undefined,
          ['chat.voice']
      );

      expect(result).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Voice Chat',
          childFacingDescription: 'Voice chat permission',
          privacyNotice: 'Detailed notice',
        },
      ]);
    });

    it('should return empty array when requested permissions do not exist', async () => {
      const result = await service.getTranslatedPermissionsForApp(
          mockAppId,
          mockOrgEnv,
          'US',
          'en',
          ['nonexistent.permission']
      );

      expect(result).toEqual([]);
    });
  });

  describe('getTranslation (private method)', () => {
    it('should handle single language translations', async () => {
      const mockSingleLanguageConfig: SettingsConfiguration[] = [
        {
          version: 1,
          orgId: 'test-org-id',
          productId: 'product-id',
          namespace: 'chat',
          settings: [
            {
              settingName: 'voice',
              valueType: 'boolean',
              userHidden: false,
              required: false,
              autoReviewConsent: false,
              label: { en: 'Voice Chat' }, // Only English
              parentNotice: { en: 'Allow voice chat' },
              userNotice: { en: 'Voice chat permission' },
              regions: [],
            },
          ],
        },
      ];

      jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValue(mockSingleLanguageConfig);

      const result = await service.getTranslatedPermissionsForApp(
          123,
          { id: orgEnvStub.id, orgId: 'test-org-id' },
          'US',
          'es', // Request Spanish but only English available
          ['chat.voice']
      );

      expect(result[0].displayName).toBe('Voice Chat'); // Should use the only available language
    });

    it('should extract last part of multi-paragraph translations', async () => {
      const mockMultiParagraphConfig: SettingsConfiguration[] = [
        {
          version: 1,
          orgId: 'test-org-id',
          productId: 'product-id',
          namespace: 'chat',
          settings: [
            {
              settingName: 'voice',
              valueType: 'boolean',
              userHidden: false,
              required: false,
              autoReviewConsent: false,
              label: { en: 'Voice Chat' },
              parentNotice: { en: 'First paragraph\n\nSecond paragraph\n\nThird paragraph' },
              userNotice: { en: 'Voice chat permission' },
              regions: [],
            },
          ],
        },
      ];

      jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValue(mockMultiParagraphConfig);

      const result = await service.getTranslatedPermissionsForApp(
          123,
          { id: orgEnvStub.id, orgId: 'test-org-id' },
          'US',
          'en',
          ['chat.voice']
      );

      expect(result[0].privacyNotice).toBe('Third paragraph'); // Should extract the last paragraph
    });
  });
});
