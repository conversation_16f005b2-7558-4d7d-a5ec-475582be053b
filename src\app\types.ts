import {
  AppUserParams,
  RequestUserPermissionsDTO,
  UpdateParentEmailDTO,
} from '@superawesome/freekws-classic-wrapper-common';

import { App } from './app.entity';
import { IClientCredentials } from '../org-env/types';
import { AppService } from './app.service';

export type IAppOauthClient = {
  appId: number;
  orgEnvId: string;
  clientId: string;
  redirectUri?: string;
};

export type IAppOauthClientCredentials = {
  clientId: string;
  secret: string;
};

export type IAppInfo = ReturnType<AppService['makeAppInfo']>;

export type TRequestUserPermissions = RequestUserPermissionsDTO & AppUserParams;

export type TUpdateParentEmailParams = AppUserParams & UpdateParentEmailDTO;

export interface SettingsConfiguration {
  version: number;
  orgId: string;
  productId: string;
  namespace: string;
  settings: Setting[];
}

export interface Setting {
  settingName: string;
  valueType: 'boolean' | string;
  userHidden: boolean;
  required: boolean;
  autoReviewConsent: boolean;
  allowDependentPermissions?: boolean;
  consentDependsOn?: ConsentDependency[];
  label: LocalizedText;
  parentNotice: LocalizedText;
  userNotice: LocalizedText;
  regions: Region[];
  irrevocable?: boolean;
}

export interface ConsentDependency {
  settingName: string;
  namespace: string;
}

export type LocalizedText = Record<string, string>

export interface Region {
  region: string;
  ages: AgeBracket[];
}

export interface AgeBracket {
  from: number | string;
  to?: string | number;
  defaultPreference: boolean;
  consentType: ConsentType;
  defaultParentLimit: boolean;
  dependentConsentData?: DependentConsentData[];
}

export interface DependentConsentData {
  minPermissiveParentLimit: boolean;
  settingName: string;
  namespace: string;
}

export type ConsentType =
    | 'opt-in-verified'
    | 'opt-in-unverified'
    | 'opt-out'
    | 'none';
