import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { User } from './user.entity';
import { App } from '../app/app.entity';

@Entity()
export class Activation {
  @PrimaryGeneratedColumn()
  id: number;

  @PrimaryColumn({ type: 'character varying' })
  orgEnvId: string;

  @Column()
  appId: number;

  @Column()
  userId: number;

  @ManyToOne(() => App, (app: App) => app.activations, { onDelete: 'CASCADE', nullable: false })
  @JoinColumn([
    { name: 'appId', referencedColumnName: 'id' },
    { name: 'orgEnvId', referencedColumnName: 'orgEnvId' },
  ])
  app: App;

  @ManyToOne(() => User, (user: User) => user.activations, { onDelete: 'CASCADE', nullable: false })
  @JoinColumn([
    { name: 'userId', referencedColumnName: 'id' },
    { name: 'orgEnvId', referencedColumnName: 'orgEnvId' },
  ])
  user: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
