import { supportedLanguagesMap, Tiso31662, Tiso6391 } from '@superawesome/freekws-regional-config';
import {
  AfterLoad,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Generated,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Activation } from './activation.entity';
import { OrgEnv } from '../org-env/org-env.entity';

// The migrations needed to be created manually. There are issues applying the pseudorandom id generators that
// keep creating new migrations all the time (lots of things were tried like adding a default for the column that
// invokes the pseudorandom function, but it ended up always with issues)
@Entity({ synchronize: false })
@Index('user_username_orgEnvId', { synchronize: false })
@Index('user_refreshPasswordToken_orgEnvId', { synchronize: false })
export class User {
  @AfterLoad()
  checkAndUpdateLanguage() {
    if (supportedLanguagesMap.get(this.language.toLowerCase()) === undefined) {
      this.language = 'en';
    }
  }

  @PrimaryGeneratedColumn()
  id: number;

  @PrimaryColumn({ type: 'character varying' })
  orgEnvId: string;

  @ManyToOne(() => OrgEnv, (env) => env.users, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'orgEnvId' })
  orgEnv: OrgEnv;

  @OneToMany(() => Activation, (activation) => activation.user, { onDelete: 'CASCADE' })
  activations: Activation[];

  @Generated('uuid')
  @Column({ nullable: true })
  uuid?: string;

  @Column({ nullable: true })
  username?: string;

  @Column({ nullable: true })
  password?: string;

  @Column({ nullable: true })
  externalId?: string;

  @Column({ nullable: true })
  passwordResetToken?: string;

  @Column({ nullable: true })
  passwordResetTokenExpiry?: Date;

  @Column({ nullable: true, type: 'date' })
  dateOfBirth?: Date;

  @Column({ nullable: true, default: 'en' })
  language: Tiso6391;

  @Column({ nullable: true })
  signUpCountry?: Tiso31662;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
